use alloy::primitives::{utils::format_units, Address, U256};
use crate::{strategy::trash::TokenUnit, vira::token::Token, CONFIG, STATUS};

/// Gas trait 定义了所有 Gas 实现必须提供的接口
pub trait GasTrait {
    
    /// 重置 gas 相关字段
    fn reset(&mut self);
    
    /// 设置 gas 限制并计算成本
    fn set_gas_limit(&mut self, gas_limit: U256);
    
    /// 设置 gas 价格
    fn set_gas_price(&mut self, gas_price: U256);
    
    /// 获取当前 gas 价格
    fn get_gas(&self) -> U256;

    fn get_cost(&self) -> U256;
    
    /// 计算当前 cost 的价值（以 USD 计价）
    fn usd(&self) -> f32;


    /// 把cost转换成to_token的数量
    fn convert_cost_to(&self, to_token: &Address) -> TokenUnit {
        let to_token = CONFIG.tokens.get(to_token).expect("error convert_cost_to");
        if to_token.is_eth {
            TokenUnit { bn: self.get_cost(), num: self.usd() }
        } else {
            let usd = self.usd();
            let amount = Token::to_amount(usd, to_token.decimals, to_token.price);
            TokenUnit { bn: amount, num: usd }
        }
    }
}

/// EIP1559 网络的 Gas 结构体
/// 专门为支持 EIP1559 的网络设计，避免不必要的 Option 包装
#[derive(Default, Debug)]
pub struct Eip1559Gas {
    pub base_fee_per_gas: U256, // wei
    pub priority_fee: U256,     // wei
    pub limit: U256,
    pub cost: U256,
}

impl Eip1559Gas {
    /// 创建一个新的 EIP1559 Gas 实例
    pub fn new() -> Self {
        Eip1559Gas {
            base_fee_per_gas: U256::from(STATUS.base_fee_per_gas()),
            priority_fee: U256::ZERO,
            limit: U256::ZERO,
            cost: U256::ZERO,
        }
    }

    /// 获取当前 effective gas price (base_fee + priority_fee)
    pub fn get_effective_gas_price(&self) -> U256 {
        self.base_fee_per_gas + self.priority_fee
    }

    /// 单独设置 priority fee
    pub fn set_priority_fee(&mut self, priority_fee: U256) {
        self.priority_fee = priority_fee;
        // 更新成本计算
        self.cost = self.limit * self.get_effective_gas_price();
    }
}

impl GasTrait for Eip1559Gas {
    /// 重置 gas 相关字段
    fn reset(&mut self) {
        self.base_fee_per_gas = U256::from(STATUS.base_fee_per_gas());
        // 保持 priority_fee 不变，因为它可能已被设置
        // 更新成本计算
        self.cost = self.limit * self.get_effective_gas_price();
    }

    /// 设置 gas 限制并计算成本
    fn set_gas_limit(&mut self, gas_limit: U256) {
        self.limit = gas_limit;
        self.cost = gas_limit * self.get_effective_gas_price();
    }

    /// 设置 gas 价格（同时设置 base_fee 和 priority_fee）
    fn set_gas_price(&mut self, gas_price: U256) {
        self.base_fee_per_gas = gas_price;
        self.priority_fee = gas_price;
        // 更新成本计算
        self.cost = self.limit * self.get_effective_gas_price();
    }

    /// 获取当前 effective gas price (base_fee + priority_fee)
    fn get_gas(&self) -> U256 {
        self.get_effective_gas_price()
    }

    fn get_cost(&self) -> U256 {
        self.cost
    }

    /// 计算当前 cost 的价值（以 USD 计价）
    fn usd(&self) -> f32 {
        // 获取 ETH token 配置，如果不存在则 panic
        let token = CONFIG.tokens.get(&CONFIG.eth)
            .expect("ETH token configuration should exist");

        // 将 cost 格式化为单位值，如果失败则 panic
        let formatted_cost = format_units(self.cost, token.decimals)
            .expect("Failed to format cost units");

        // 解析格式化后的值为 f32，如果失败则 panic
        let cost_value: f32 = formatted_cost.parse::<f32>()
            .expect("Failed to parse formatted cost as f32");

        // 计算并返回以 USD 计价的成本值
        cost_value * token.price
    }

}

/// Legacy 网络的 Gas 结构体
/// 专门为不支持 EIP1559 的网络设计，避免不必要的 Option 包装
#[derive(Default, Debug)]
pub struct LegacyGas {
    pub gas_price: U256, // wei
    pub limit: U256,
    pub cost: U256,
}

impl LegacyGas {
    /// 创建一个新的 Legacy Gas 实例
    pub fn new() -> Self {
        LegacyGas {
            gas_price: U256::from(STATUS.gas_price()),
            limit: U256::ZERO,
            cost: U256::ZERO,
        }
    }
}

impl GasTrait for LegacyGas {
    /// 重置 gas 相关字段
    fn reset(&mut self) {
        self.gas_price = U256::from(STATUS.gas_price());
        // 更新成本计算
        self.cost = self.limit * self.gas_price;
    }

    /// 设置 gas 限制并计算成本
    fn set_gas_limit(&mut self, gas_limit: U256) {
        self.limit = gas_limit;
        self.cost = gas_limit * self.gas_price;
    }

    /// 设置 gas 价格
    fn set_gas_price(&mut self, gas_price: U256) {
        self.gas_price = gas_price;
        self.cost = self.limit * self.gas_price;
    }

    /// 获取当前 gas 价格
    fn get_gas(&self) -> U256 {
        self.gas_price
    }

    fn get_cost(&self) -> U256 {
        self.cost
    }

    /// 计算当前 cost 的价值（以 USD 计价）
    fn usd(&self) -> f32 {
        // 获取 ETH token 配置，如果不存在则 panic
        let token = CONFIG.tokens.get(&CONFIG.eth).expect("ETH token configuration should exist");
        // 将 cost 格式化为单位值，如果失败则 panic
        let formatted_cost = format_units(self.cost, token.decimals).expect("Failed to format cost units");
        // 解析格式化后的值为 f32，如果失败则 panic
        let cost_value: f32 = formatted_cost.parse::<f32>().expect("Failed to parse formatted cost as f32");
        // 计算并返回以 USD 计价的成本值
        cost_value * token.price
    }
}

/// 统一的 Gas 枚举类型
/// 根据网络类型选择合适的 Gas 实现
#[derive(Debug)]
pub enum Gas {
    Eip1559(Eip1559Gas),
    Legacy(LegacyGas),
}

impl Gas {
    /// 创建一个新的 Gas 实例
    /// 根据网络是否支持 EIP1559 来选择合适的实现
    pub fn new() -> Self {
        if STATUS.is_eip1559() {
            Gas::Eip1559(Eip1559Gas::new())
        } else {
            Gas::Legacy(LegacyGas::new())
        }
    }
}

// 为 Gas 实现 GasTrait，提供统一接口
impl GasTrait for Gas {
    /// 重置 gas 相关字段
    fn reset(&mut self) {
        match self {
            Gas::Eip1559(gas) => gas.reset(),
            Gas::Legacy(gas) => gas.reset(),
        }
    }

    /// 设置 gas 限制并计算成本
    fn set_gas_limit(&mut self, gas_limit: U256) {
        match self {
            Gas::Eip1559(gas) => gas.set_gas_limit(gas_limit),
            Gas::Legacy(gas) => gas.set_gas_limit(gas_limit),
        }
    }

    /// 设置 gas 价格
    fn set_gas_price(&mut self, gas_price: U256) {
        match self {
            Gas::Eip1559(gas) => gas.set_gas_price(gas_price),
            Gas::Legacy(gas) => gas.set_gas_price(gas_price),
        }
    }

    /// 获取当前 gas 价格
    fn get_gas(&self) -> U256 {
        match self {
            Gas::Eip1559(gas) => gas.get_gas(),
            Gas::Legacy(gas) => gas.get_gas(),
        }
    }

    fn get_cost(&self) -> U256 {
        match self {
            Gas::Eip1559(gas) => gas.cost,
            Gas::Legacy(gas) => gas.cost,
        }
    }

    /// 计算当前 cost 的价值（以 USD 计价）
    fn usd(&self) -> f32 {
        match self {
            Gas::Eip1559(gas) => gas.usd(),
            Gas::Legacy(gas) => gas.usd(),
        }
    }
}

// 为 Gas 实现 Default trait，方便使用
impl Default for Gas {
    fn default() -> Self {
        Self::new()
    }
}