pub mod dex;
pub mod pool;
pub mod token;
pub mod consts;
pub mod contract;
pub mod discovery;
pub mod filter;
pub mod sync;
pub mod status;
pub mod util;
pub mod errors;


use std::sync::{atomic::{AtomicUsize, Ordering}, Arc};
use alloy::primitives::Address;
use contract::Contract;
use pool::DexPool;
use crate::{connector::Connector, tools::now_str, CONFIG};

use self::status::StatusManager;

pub struct Vira {
    pub connector: Arc<Connector>,
    pub contract: Contract,

    pub sm: StatusManager,
}

impl Vira {
    pub async fn new() -> Vira {
        let connector = Arc::new(Connector::new(&CONFIG.server).await);
        let contract = Contract::new(CONFIG.contract, connector.clone()).await.unwrap();
        Vira {
            connector : connector.clone(),
            sm : StatusManager::new(),
            contract : contract,
        }
    }

}

//test update pools
#[cfg(test)]
mod tests {
    use colored::Colorize;

    use crate::{config, tools::now_str, vira::status::sync};
    use eyre::Result;
    use super::*;

    /// 测试mev macbook m1max
    /// total_pools_added: 56001
    /// 获取pools耗时: 42ns
    /// 04:27:59.873 0x79fBf2d9DCfd08d2345A4aee256C20c4f53FEb49 len:0, success:117, progress: 500/500 (100.00%)
    /// time cost: 3.822673042s
    #[tokio::test]
    async fn test_generate_mevs() {
        //使用config/pls.rs的配置生成vira
        let mut vira = Vira::new().await;
        //let _ = sync::sync_new_pools(&vira.contract &mut vira.sm, vira.connector.clone()).await;

        // 取出前500个vira.pm.data.keys()
        //let pool_keys :Vec<Address> = vira.sm.data.iter().map(|entry| *entry.key()).take(500).collect();
        //println!("已取出前500个池子地址 {}", serde_json::to_string(&pool_keys).unwrap());

        //test ******************************************
        //test ****************************************** hex-hrs
        //https://bafybeicb2hlad6zs4kc4yvn5xbbzti6krjtpoxrysg42d4e5s5oubbipum.ipfs.dweb.link/#/address/******************************************?tab=token_transfers
        //test ****************************************** hdrn-wbtc

        //let test_data = vec!["******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","******************************************","0x39354de97366eccd231a41c0f8e81b9938e4774b","0x1fd21a10bd3496d371e8bdd5f318fa2566729f99","0x0ce1cde2955d54e6670d035292669e9af0f5d76a","0xb1c60eb82e727c370847efff4c1ecc50666cf872","0x34226b75f8ec7da4b1618f0669e5d3edd8daaf75","0x4006077bcf6fbd69b97376aceb6f5a9c2127ed23","0xc6b2b9fc1c134bf8e58fd04b2d821ef91dc78213","0x5d3ad9743109cdaf46b2e7bc628742ce662086d0","0x51980d3b5fcaf8596fd0aebe1d2482d41329d609","0x117d07fdc816ca8715cc8b91249ddc02d6bf34a8","0x0c6aa0a04cd703e0c6a77e37de4f22c91f727c32","0x5b6b3eaca43ea6d1e4dd4006fc905a419ce42cba","0xd8ed75e4b70ada866e7bf5b8017f7b2930125449","0x6825133f74c341f4d55a6555e092284411919314","0x069cfb91001e42f1dcd899b5465fed21343f04d8","0x0401ae4922f21afcc69a52c9eadae1603d7a6e69","0x01a1db56a8ddddcc32524b6aff6ee0de9dd4a8be","0xc6e4deed5b77b7e06ad2eff8f09d00484cdcd6be","0x4361dffb600f1046618b9a8deb4433bb70057dbf","0xecc8004408cfcb1e839c23f75b9d9240eb99bd0a","0x10590b2a340d1c4b1905d1abae84e838d26cf03b","0x5075965ebdad346439f7ffb6d5b9adeeec8c4d90","0x18ed8ca8d5e6b32bc17033cf1a021c416b682fd6","0x0f64cb08d01c23771dd4ce443f4246627a25ca77","0x4048a0982a29e1960267e9380dd25d64f51b6c6c","0xa7ee5813fc909aa27984309c711da7fe17af875d","0x89ee00c46b464445abef6a93a8ffff70007af788","0xb33550c49637f3c614c763546e9d5b98b311f38d","0x5325da5de851d040ce5be99d681f7a36d1f1f5bf","0x2c1ac7e59393fe6954dec6e32d419a136b29c352","0xb6da94d09cce4e0bbfcbe6f5fde46983c14d51ec","0xe6344bce2db077deea328c7aea2a79e0d771daf2","0x4cd0276d374baed56626bfa489e1c9e6fdf6c669","0xe895ca9d77a1bccc146ee824ba0bda229c228109","0xa24e1bb786e192c0eab7c77a2b5e97f00f569421","0xfdb7d9140e582ff330122be3ba425d2477d4e09b","0xe48fbc2636a852b75abc3e6703b072f8a0243d91","0xb909f3918d4bfa8a61768f68a26bbe0988950786","0x15c3503cfd23d42459f660f46a804a6ba453686c","0x5ee3b6ef0058c432594d0219b97a8a36f8641492","0x99b6fb004f71269b9a85b9ccb4bbd84836b67921","0xfa468f98d05612c9e81a13ce96906d5c7ca1e809","0x11c3eb023fddba15503a05fa864b47c8700e20e2","0xf565c2df0f56cb7d5a1b7dcd51d98285a3cd41da","0xa19dbed412028869bbadf33bfc61c1ad8c9ad778","0x37908fdc897ed7a9dece8cdea6c08b0086109b28","0x74d3d186f053dba8a92f79ff7a1c539473abcc68","0x321c9dad44b6d24a3495c9f28002152872bc953e","0x59f214afa42f39084a25b8adfc34b2ff9fb415af","0x22155b1755d377495975c90e2ef1ad80d805d3b5","0x870f6d89222f01609aa4c417e8f8baaccef7f979","0x3cdc10402378af3bb854a9e6bd8c091f5be23578","0xa7d91d67769ef2fbcee6662fd1353dbbe2c8f079","0x1e19a7384b53a9248cc6d8d8a703669b5b0aeaad","0xbda3ae72e915fc72b9885b5921962ade2aa96ac8","0xf6a27db34c23d8ce81e539bbea2eee5ea8879ef3","0x0ffddd593f6ba8e6305d2ade9ed3ffd42bdf483a","0xc9e57d8532ec96d6813e1b17e4ca5873cb4d8579","0x6b6e7dacfe92100a84f862a530fdc11124edb26e","0x1e08c7b314856793d9eac7155ac78e126085203c","0xea77bde49290e99a5543e2118a5de8519e34798f","0xeb65ff456000805fba2174bce179e71f523c365b","0xe718c0630910712c1a0ee4035af15aec9d9aba9e","0x576cf853a18271b30455b8a6830b818bf3ab917b","0x0fc1bfe2e94612b7973c6104c7033a9a4c4320f8","0x342a663db72b42a34037d97efcc94eee381b6b35","0x97ccbbeb7a2c94ed3c07344393963418b8dc1669","0xe97e0333bde884dffeb4cb2f42209d166446074c","0xd445d61a719a068121a6a16e493338d7b3c9a9d2","0xa47c1e82f33d916d9865303d11aac8cb1b3863bd","0x9494d86d9237dad55ba4559ad9607bb8d358d584","0x2f8d68c8a61371c9a32453299ae4d9902057e021","0x10874df0a509d89ba5efe0ce33ae8bb0b803a9f8","0xf18bb32b0ea9a05f4e3ac6e32f62b391e71701c7","0xffa477faae5f7b838986729540ed7e755b7561cc","0x00372f9a50e61aa149c552f95c8508ac3f6fa2c1","0xc2cb5dd58725dcc0541fd3efd2b7a63a88273844","0x7af426b5e4ac08c2fecc820941efe5b091e355c9","0x9830353fc3c1d5121aef9c83f8e653b9aca10abe","0xf97abc75b6a5947566f386642d7ebd9d500595cb","0x627ab518ffe7c0352204226925deb651c260709f","0x55d1fea53d56489e41642e449e8b157276387ddd","0xf374db24a901795da50b9f4022274f7d722abf4f","0x33936f0e1e5d8c326edbde0f7b612f0ff343ee5e","0xafd741488cbc7308d4cc1c2d311186352aef692f","0xbdbf33121eaf465bc0abae207bddba6b7214af96","0x006cb9eaf24f785c7382ed61e715143775d1db0c","0x93f2196dc0a66586249b0df5045988c0a45b3dd0","0x0c7e03502dc90d39586d759c73ba1d3030786ad2","0x93877ef434019682ce82d7417fd6bb6bb2d7a6d1","0x9310bc3482743df161c345e71b2dbbaffd37b978","0x1022716a8be0d8776ac6f3a66ac52909278ca7f2","0x67df90e4eab81ecc38735091bf2e86c3bf2314f0","0x80c7f9dd05cf865bbdf6132932b5ee85e113804e","0xe1810b8c1d3f04cee4487ec1fa63dc51ef3ccf33","0x5e29576854c9c6cb4aa08d3ef62c47d509621eb0","0x707b64f52772c1b65ac54795f3d9abe88b48ba12","0x0bbf2ca708c576129a959eddeb0567acb51a1251","0x3eaff0d02d187be9e1dfca1c323b720ef33297de","0x0897833e6aef9961b8f5256705e08ac2f06e031a","0x652b24c3e2b2d16fc4d44036cbe6b68f65ad17ae","0xbfb863852f74c50bab975cafa834ea5afba73ac2","0xbb3489101d1f0f7aa86d2c0abb2d0e18dccec4fd","0x5cb87974292054d580021fa4ca74e77b305d1bb3","0x649b4fba3e03ddfb6fbd789f23a9922977145f80","0x9e9c092149e8f46528f01e0b8249c04d9dfca5b8","0x77be784a38b0ceac306fb0004a89882a7204943f","0xff7f5da74c315ce3fb0f4d6d5ee47bcc109baee7","0x41cbf686e0f71f3b267f3712c0943c7ffdc85a7a","0xc8b6ff7751d0f7fb380e29e1093fb7afd19e85bb","0x91105a6296b55cf8b85298f783925db3948e9842","0x738af5e506747bbad9934c06e961eb97ee1afd4c","0x342f42808f1c8f41279b421c41022b2559989b88","0xdcb04a9708a3ad08a87a79ed7dfbf6f390c93cca","0x953ac8b6eb8510ac1a19e4a2a0e339fc3c697adb","0x22192be7ba53dd83c4723db6d17e7eb80fc3a561","0xb2d17154e118c610cc035eef4a503b75dcb4deb0","0x49e98a2bbdbe300dd4dfdbe865a7e6f3dfe1d902","0x556ed1d2b077fa3a778a72fe1cbae5e29b94490a","0x541def9878ac82294dfa4e3dc0708495c87a1208","0xed5949c5c92f5c3d352f2782c0471d44dcae299f","0x93246c7e67b71d016d06e1a8f48086bd91e04788","0x2a2b7f14aff9213dea839a67c1906ebb5370f44c","0x2f4d54b37f2e0a7e45b7d538862651b921e39932","0x7ccbc3361ca7cfc14ac49a6420a7cfdaad1fc2ff","0xd85d4c2877638de840918a79b2f8ca340ab40e38","0x8ae7f4167f8f8b1fe9a6e2711eb109b30951dfc8","0xaa076c212da27c0c2546784b3a7cbc3fbff03c27","0x8799191167da03cb3d99dcbcaea2eecfe7996a0b","0x2c2b7fffc3ea8140908a2f029a2e9d77e633b9b8","0x57c2e6c091a6a679ff7b8ca4f4d2e64f45dcd3fa","0x3a2d8b179bd8e804fe42cb73c38bc58b99db8894","0x4772db374c986ca283222d5180e3899521747140","0x1c57203c0d8dd7759732d3786d867d482d17e4fd","0x01029a04ceb104a748c7007a61b4dc2da40dc18c","0xf88f8d265212c871ef5a7eb09483d7376d85d453","0x7b1768fa9bbefb85cace69004edb80d1bac61a7b","0x463b3c8166d3390aa328111a3b0e9db6937e5382","0x84bf0d1be7acb7f5e63b510c2af8ca713fd33dad","0xd160bfd645abab935161aaae92ac23f645637d0a","0xdab7268420ea504619b4bbf85fc4ce75dfae7096","0xa33cfbc67c3af48190f6b3f8812ed3e6b401d5ad","0x7117f6ac799b5f08e560e97945ff2815cdc7a110","0x6b89ffa5650ca307ea7d192328552c1dd2757034","0x48ee98a6eb6d224e12b6eb86ef176a6b306ca43e","0x56252ac5465674c5857e483833b16b9e7cc552e6","0x41f0b43e5976645f8260da573c1b64ef31bb5f91","0x42c3ce4839bfef92734d8f260e9dd39ee0d9344b","0xc17960030887799995f15a9fa8322fdb6f2e5ff5","0xb6e121ad9b874701b6463929c3bef7eb599dec3b","0xda8cb700a8bf05892593cf7fe95aca3cad312491","0x53371872a55d89ad5c540b740ae84a314187ac46","0xa1611f590f75d9940e8d7543666478218a938369","0x349f0e54844f788e13d5d8d41dc55187e7434c99","0xd76e4edef46e33d569f73f44c6bdb47b85fa47f7","0x5a4b54c6b525f2aa26d2606f0965186b4d6ed9f6","0xd27ad67c9dba2018416074cdde8af58e40440e55","0x2029662bc6c10c022dc59d6c37b7c7df097874b0","0xa3a7fab41a6ad5c94cebf167217c9cf67439d72b","0x1f99b3f928d782451a4e8710cc5b97a390dca2da","0xcb77f93fd2b60f4b4ebc716dfd35e0eda4b68dfe","0xab8cea09ab3dfbe7d99d4a568b04690c99583ab5","0xc649dccfbfb8659e18ab883c2c813e89f2bb1873","0x088f7edeb070a7c78a357c8dbf15e7f04a324ac5","0x32eeff000bb05c2640d3296ad6741d952b5d9227","0xa991335544d4d3bb0637edab66337de8896cb9fa","0xe91c95ce00fc7efc4c304496c5f81e2dd12b7422","0x39cf5e3aeca66a9885008fd84c8054c448ae87c1","0x49fcb4d41809be820e14825ed5ac67544484b751","0x79097920338d4d3bbe9a9decb8a8d812ea9b7eb2","0x7e45572aba9d1f303a3c1dd1b281a96bada94a5c","0x229bd36f66de73adf2e8908929f6ba4062463150","0x4b7be0fb1feedac899d9e50a417c6b61fb31e679","0x292bb3b10e7149b96ea06eccf97f483640e38997","0x8ca25f902116464e8c28359235e6db5e034ad2ba","0x2638b7ffacc9d6d076c7dfc12631c4fc0d4f400f","0xdfdfa1b865c9753afc6c30b80427593495e56a39","0x5f0fd2ec9544bb4e6ed80c9833c454f72b4ac4d7","0x53544d8323c52bcc8299c3719783ea4ef71d057a","0xdb67a23162410dabbe03239b77e626547d5843f3","0xdbac4004e8df0844a28f17aadad4ad00bcefe40e","0x068b244035baec7db4f7445f239103dd4d983638","0x2002da55de642d801f8872ff02e76bd43b335848","0x6441a78049c729c42b699fc43c4c8b406096152c","0x2bd04f41d22b107aecc67e49dfcf4c20218c0874","0xf9e9341b61934a51adbf6ee953f229b0550d79fb","0x39723eafb59143635c75e25cab786da3814bca8e","0x1458474d32af22a4e0482135b1a1dc8fecc39343","0x6d65b60fb188489a0d2a47e1529a47433b4887f2","0x57f87065367e51fb6adfcce8190b62280301fde8","0xed45295dbf9eeba96067509408803b8138eb803a","0xae7f70e43289e92339639590bec805c8ad40994d","0x0cc467343e714a386435f83dfcc1334ed9c57f9c","0xd145b9618c24ca08eb59c40089b7f74d71588787","0x93b034f1a55c877a3a5bd4cb80bbc43a1b1e6cf3","0x3d92dc733737aa48fdf3994da07b459510a18c04","0xf049fb1bc6116063f92ba72aa8f1dd45fe560ee8","0x81318556b9fcdbee620a2a7475de4f540af95583","0xbf4cad550843e660a921ac31cefd4c0c3fa6a93e","0x12c6072e0c8c560f632036cb6db1ff7964181067","0x4484036d0d7e7d976fcfe93381eebbcbc2c6c13e","0x505b2f84315a1c1fdc12b4d57f9c7e780fe90989","0x40cf980edc18a064524484f27d1251b2c3a707b3","0xe12db97e698bb6195a7f15d6969628cb9b2e2448","0xf38b618966df933b26cd76da9637bb3d2a66c939","0x9df404473bba4b96a26882219e008e7c2daef6b2","0x46c6b7d3e4f1b746d706bef76d2a5d686c4ac0b1","0xc035fbc3ff01f228afcc5e7318458621d2faa5ef","0xad3a153a7e8aecd04fafe0c872ccaebc22c19799","0x2cdebf07907602d26d9cf363672892b2ede0e742","0xf7cef709887e8b0df30f2b9072c6be3024d852b0","0x06f8b2297d49bc1ee0bc182b4d182851e9558ea5","0x5f2c57b5410b5245e5ec153127313a94e4c88a80","0x5ab43be80f9fa45089c865c1286e933fe22bf2b0","0xee4df975fa802e021572a5c225db260ede4615cc","0x130c006994c62170b911bfaedb9eb36bf8021875","0xb8abf6c79759adeda309035187591737c732ff77","0xf7cbcecb2f6f6e7ae57b409efd6bec8cbeed7c07","0xe3de5484a818b5302999ca4155be9dfd47238493","0xcb2aa6e7c90b0d70c02d7771f03dd0554c67b656","0x6bd74a68941746a58d34cff26babcc38af10ad0b","0xf7be33a5d6faf579c25f8cda12fe9db8cf3892bf","0x6b55a578360adc2b06c11648e710c78caf320bc0","0xde261191526546f68acc077f79d552f90aa12c80","0x06ea9014446b5f656864320a1cafc9684a3c37a2","0x527dcec01ba213eb6e363420bc8517d48f952e44","0xbd04fd75d90c1a932ae90dcc92f312842c60bb28","0x965a840b85e43e0b203d3e7e5fec2d3a0466ef91","0xf302b77ac0e51982f032cc6deab46f3608a5a51d","0xf87d49aef50fa770c2319c331f99adeb965dd2e1","0x30a8879b031eacafaeec14336f5230410d0513b9","0x6f8f53e6c5cdb9cc7539127cb86cfba170a55fe9","0x729fdd103acce865ed9f29076a1cb3021dc61a7e","0x9e0de20b8afee2c7352e191f7702e2de95777a2d","0x560ff425806315562aa397a42926b2d195507ea7","0x7387b956c8665b50423598e2028087b11690eff3","0x974d00e13d480e578c615e80f3c45de0315ff51b","0x7da9d3b19b5b053b3b9a15b5c8d96aa6586077eb","0xa55059a44f91e98e8e2e0104cfe1582fa48c2c36","0x148136365ac503ab7349c5993e99ccfea2ad01cf","0x8dbb0a7382193f6d505b6edc2ad226e9fab323d7","0xd89ae357f7063640cf0c761b6cd66fd9ca8c7eac","0xb02c9b4c7eaf7fe5df6db54936b028fa93b630d3","0x880250c20bb1e651fe9a59e1278623a9619d95a3","0xf8d0b162c222109bdc1919507a66ea75b76551a9","0x047651151e9c568f17c28c2923eda3050497935e","0x74823d08b27b3a42d35442b013dd35fefb5c6af2","0xad0f563319d8f209d22d00bd7ffb99180b818c71","0x2febc5a29a599879fbe1527f611cfbfa5a95f23b","0xe3f83e863e76c9a645619f59c41ccb3295c466c1","0xe7232f7b552322befd213ed077fefb00545becfc","0x354e827b41ba3afb1ff428812ea256ff90a34d21","0x70eb7355b2f8487e62765d809d58a44c417ad1c0","0x36d26c73725c68c3b08ab212c69062ea66db0c63","0xeb854ace8a4ab49b82dd96a7810222d66ca74fb7","0x587b83e1fffeb103a930eb0d08e3b9581a1d19c3","0xe386b876316b38f0d43a97e622bdd7e3ac731977","0xb9b4b383848f6dd937b6f2f6d3f6953dbebaf0ea","0x70959450ab233b14546db5d0f117fa20a286dc94","0x7f599fb4259ff275602390d25b3c4d631e59b82b","0x220f59fc5d8d26d44c7039492801e56cdc74539a","0x93655ff48cee45d2406b7c574be9b5a183fecee4","0x4daea12a32d700b32094d9b9b467370607d86479","0x31113f3dc11c5edbaa140b6964b104d7f228df99","0x926d03e2b698bc1990b4eab804ec0afbe40cf6ab","0xcfe671d0bcab390aabe06a268221ffc1343835b2","0x88cc0dd1e6ed52f2d832d1997a7b63f9d6a21dbe","0xd28a9791dd1adb541b2d06fbbdde6e5dd4745dff","0x02092ca44f23032e9de8cee6bd7631730cb7a527","0x2cdf4f36658edcd8c0a9761af6bdaca1e5a6dc74","0x3f9dd8637f17a49df1215e8d822c6a97a264e906","0xb14777eb6e4b19674257ab3b67e683f481a9eb90","0xe75df56c3434b3aa3bb364f2ef85d79d18f5ba17","0x493026cf19d60abff86dd9d1e619b9aeda089ce1","0x21a56434ae2a2bade03857f1958456ef0e09dd04","0x530b28230a62c8faa96b029125cb778aa737608b","0x358f98ffcfb77c68f6a8c4c72b624cf460d66237","0x037f95f2ff2e44dd644d00007d7c3945be53aca2","0xfaf3ca82fe150bc7e62aa7d3cead2a3302c3b145","0x19eff66e232475bbe832e24460429b85213e95f7","0x91417c7b17b3314db43b31236f4ea9270b14c902","0xd763134369f331f184d06378dfaafacfcee4511d","0x6fc227993cfe2114f1ef57e4408c3314da22726b","0x06ef58f1cea4a622da4e8b1f9dfcab24ec0238b7","0x4b27608fd22959212a691120e492bed58ed198a2","0x17646b04e039cd5e49f0ca6f0e7240900df4571c","0x030e6c97cd2940cd691d0dba791b5b864a8a8a5e","0x0b2c03c2d5839f5fcfb48ef5bd4a9f787b95402e","0x2573c8c0f3c803eb61e3b7ead11adc40e54d84ff","0x902736a5940ef4c4c1398448173316a4aaa5f64d","0xa72a8be84577e8f27f120c3658c064af8237250a","0xc4d151303a163e0218e57eb4ed42e7fbe7d97447","0xb73c36893313a8fdcb3aa79732e3ecd87442395f","0x5fc5fce24cb61fa9d56738056cf593f28160d1ed","0xeb83354862a5185e847968cb74a674c062db0b1e","0x7287e3e3e76e114d703e22353e76a71debee4d89","0xfd71d68aac59b96c236f754574ac73b7b4f81ba2","0xa1362af1e7c42fb31be3494ade589d6acc7b0e29","0xd8f5a821bb3c4b157b7a9867270c2f4e5db7c898","0x48dfe19416889d068089028015c54e32e1525d5a","0x61071c3f9d68e6d24f65e72ea28647680f132ecd","0x2610ad47639d3a14b93558d80ad94cd78617e757","0xb20a0c92eb71f372c87cfccbcc48a69e6c60fe1e","0x590cb5f39a4a771c350434ae85892587a4635267","0xb450ea8e09f85ef29a5872f4ce6b7edc72616420","0x37d86fe9e3b93681b8519b841f028eecc8aaa518","0x6dd0148d778a5bcb4dd7c4550d38639f005dc9c3","0x6af036a516fa5c915796e8300145831639180319","0xe301d116c9bd3332202d98266783fd5f2112bc4b","0x9fc74dc2451bec89363fedad2c70476df844b3f1","0xa00790f5fc1c1d99e313c8f84a179d6f02aecee7","0xc2221fc71ec06399cbc9f0dac5c364bf09177e87","0x894b986909667a69c30c04cc09e16efe8a420578","0xb3f99383462ff65266afca106990a04062c67919","0x34c818a652911281943d206b006bedd965a7063f","0xdd202bbcecd499186e876398deb2296d1071226d","0xf78efad3f08717bbcb84d75a27562285635edbe7","0x11c093f787d3d0959ddccdb139255a01cd1b61cc","0xe6c09a0c02e46bf7826593169e1b30de4e5b32a4","0x88b2fae4c22cec7fc75f95db027cadca8617f833","0x6394b872d432d7d2b78d4ccb5caeec2e1e115130","0xacd678d75d4a6e199e71a440559754f1d8dd15c0","0x679488fea97447905475217b9220168edff0015a","0x94821ff290100e41b0c8cd6a4b22fba7611a9d7d","0xb779e0695df07162400ac137f55e917ea830d6c0","0x31560935314eb382ace5e1654953d9f7f9b65a69","0x2e09428c73316ce8658e29562408e29d5fcad971","0xdb236b0fe42c2b2d8d1e16f3e154aa4116414911","0x4f43ea6f50eda43d6e07dcd61a30b61a750897bb","0x9f76a81c21a4033988e9983cabe20e4e301915c0","0xfb6dffafee6f5a1b1e5a2ccc3b1560823d76ae2f","0x711cbbe537238cfa4a2f7c86eec218d6dc71dd1f","0x0fd8523e89c271591075d2a5b9d49339caa4f628","0xe0a31bd1d393774746748bcc17c9ab669f7d4307","0x3454078bdfae34e56c873a54126fa9f88d6833ed","0x76c71db6f0ef8432ae2a415e74f71bc824e81d79","0x8ca0d80bd3378a2283a477c5bceb4e1bc4e2f9a3","0xbe3962331f32666cbc1d66c03695b1d0e0cfbb55","0xfb4baad808d09b7ff98254613048ef5f836eb869","0xf8eaa116b584c63d9484279e2821f5ca05215903","0xe79509e1a742e6790299041c12673bdcc7af1965","0x9d2170d626d51e407a31558ab898ea8eea13f0c4","0xf282fa91448666e53f4019e8ccc0ec0478153b26","0x3f6bdcbecfe28eddaf74be4215be757d3e515c1a","0xc9508a33ecd65d98b6facaa6fd53692b3b805548","0xecdfff9d6371ffc6884eb95ed432c4ec98b0b866","0x4604da2dc1e0c5c998c44764d900e39dd45e56c7","0x3c6651216ac7bf322d934c935b5d8d406932eada","0x71250cc50d24389c57ee0f9b8d72831d63bd3316","0xf7bacb2f53fbe5c77041c08d170064a6bde41ccb","0xe1d51297ffc059d421094bd5712d0bc63f6f1125","0xcadb252b410a00bb0d5e35aa99e15637c0734805","0x3d608747f215e6754ee3259f4453cf4e693fc947","0x279fb7271f0885ba0d066448eeca01926b7eba67","0x2213a67c1a0469398dad8cfc06239a7b6055169f","0x664ebfaad68f379ad12bd229ee0543faf52fef60","0x1d55f0a69e709262ab79ed7f554123268cf813cb","0xd683063d8f39ca195ce59c5f260beb154a444e20","0x92b7370e3418185faf5a9a8f12ff2dafd3ac1e7d","0x9b8c8941d9a446715cccb77e683328d161d5a279","0xdb6537be7254e2ff1864287ec18a5ad2341c0e1f","0x36a33f971ac691ce85eb70e71f7a2ed6ebca7140","0x290ed9311e3a5b0551331ae6e92c84bf076ccca4","0x15732df8d34e741c47b397dcbfc1955feac5795e","0x85fd611f6e452f0a3e7e321022df12ba4efa94cb","0x03d7c6015ce35484f8b052c37135c77fe0170200","0x882af09bf7f7d362f934ba484c24770710348307","0xc6b16cc2887014e3d81524c4f2f93be502ed529b","0xfdb6b5043abfb0e981a827e73b74b269d6127e8f","0x42d55cdfcde19ec1c8a4770d11e361d978a55857","0x93a45a42c33462d1271cc068ca210066dc9c35a5","0xcca78c763bf87b6849c4d2696e933a06e804c18c","0x6886becf1173ccb01b9bb8647f6cfcd3790cb409","0xe50f9de0a1dfb3d0078aff56e356c63a0c199742","0x960670a43e8775d5c680dfe77c51d8219b145861","0x51e4b57b347af19b834579578357a677e01abef2","0x23201710a887fe8b596bf83a65df795f0b8cd068","0x77a1d27f55f8ffece583f933fd28c9f85ade5d33","0xb01444727d66d555fd048378f410d090b1401e63","0x9c97bf03251f3b6c401feb7ce4d997c019218411","0x6fd0f9627133ddb179ae6fc66a2918d3f7c00dc3","0x08557ec43dd63d9b433f8c399f1bd01280451efa","0x8719c911f3d15f3564f64acb9341f37fd9e8c762","0xa06cb7b0d2884cc9bb75e4487fb717dafa0f4952","0x2e3ac557bb2c9f73f3a35f646a84cf0580e57435","0x84213e2a6c5b18f63f7ececac9c94f892a659c58","0x2f943e8307f92523b03aa390ef5d06c143996e1d","0xf1cd70b52c24a1c58f027e0740bf8f3ad60617f6","0x973adcd37c150d480538c650a3efe17ca5c53949","0xa3a8fd8908f09bbed6eb30b610138542c04e6d9f","0xce65998b5fdf360e84cb65fa9b022d44b767025c","0xabb718f7d49e2b9bf6fcd92595390b69b4e49bbe","0x522d821e8260c3982028e110f5b8245184ae62c9","0x1f716c7231b7f1ceb9b1538fae748051cf1b228e","0x441bacc5a6b003a698ba71d4bd54f06ec69ad566","0x14add98137ee2622979fea66d6ebb31170ec47f2","0xbf9daae281aa792bfff02b6ffea33f3f5043838f","0x08c9b4a373107d722ffa99fcb8abac57c7b085e1","0xbcb90aee1e62df3a399dcc622a54a684b369d6ba","0x3a497187160ec13fe55e4fccd0909cf658ddedc9","0x27202821eff25207ed200e4ad7b0fd0d8a035d59","0xa4d8eaaaffec7455e153768f54a0773bde5b0da4","0x8444ae71d7e90e771ed7e309e6899d3f806082e7","0xdf7de25c1099d8500035aaf3b0bc3ce37bea8680","0x76479dac9847ae6d43f2b0f24c08fd9012e448e0","0x93d72b297e3a0de016e5eb504c70b4754deadb14","0x84f177c05416abd44b2a2a0c8e4272cf007f9578","0x296de7271fc583031ef37a2276c44f82e6d90fc0","0xc14e082df04f8ee88b34db94ddb760ee53f8585c","0xa00cb74bbf1d6c755a69ccb5deb5d7de8f042f7e","0x02e8b3a03c3038e68a58b01de058bf05c0a058b7","0xf5e807d963a43c9438c7dfa3a604bd1518edfb29","0xe763dfb11bd14f8982e8f95712459db7f85814dc","0xc62258e326b0a69ab5d5c6f07caa16eca8dd3590","0xfa93d1b4052268354f012a27171dc7a8f69c6790","0x3e9dda89b626977f68ed7167ed5fc50939f151cd","0xade89c73a1c443a4a290423d04c710fce52a0628","0xa327a799c90c3eecb95c0987e0e980ea8b4eb0f0","0x0b1565ed0e2b3a20a1934446f2390d08ebe3ede0","0x974e48ed79524dc7dc78dacb045ddadcc35c8dcf","0xa4b5ae1c9b2d014d0f64fc806d39654009fe41c9","0x32cf2c39ad270646cb43202e682841f83a594700","0xb7475335005b097836f81113c9fd1c4ecbb9e01f","0x10fb6c42228a5992a2cc9b1fa7802e3f67bc39e9","0x94e331783a3605ef7d4210a9a8bafa0dcc8f3bee","0xe5951e19f6fc63f14fd3ff193b6e7f5f18a8e059","0x975fba90c73d1e33cf7b5229145df8b5091084c7","0xf5fed28cc7fc6e62850b00df47b66e01a044fd73","0x47981c7d075679e4ee3980efe38523607d89a297","0xee7175b08dda50f4ca2faff3e9a2e90d6dd5a431","0xb9dcf3dbd0a4fe614dac65a8f9ba9e078627836a","0xc2aa1ff6e1e7f46bbb9e5b1a7f5585a4334f3241","0x413812414312542f9c8f66ebc2985ea66ca5436d","0x757b8d7dec1270d8f77a36a8bddf225cf01f06ae","0xc2277e7d1db190fc89da034522f11083c932e4b4","0x531e62eb8160c88ee05e9bbba9948f1e4ccc217c","0x23fa1477b763837b4a288ec135155107a774aeba","0x1b9df1cfa443a2c0977761540285bc29f57a2dca","0x887e8c5e00978035d97167dee05291eab81bc36e","0x63930f595b03d66754609cf8633f83ed053b1c18","0xa58cbd6511f9f14b776bb68a87ff9d7e68dc3deb","0x40b844b1b7a1b447771761f90ecac057c91e994f","0xde8999e8ba6a79a38a7d52041104e708fcdc94be","0x10441fc52716be137effbb51cbc2f47c9eee07e7","0x569dd5902eaedcea91deabeb14c7ff8eeae5863a","0x138f963fca42ebb4c2d5dae902767ef6f7ae9ba5","0x4ab6f288720c5635951a9222763f9fa591da5871","0x03b98136d3890304b788112ebe240ae9d8ba24ba","0x9b3f4c9b4601b6641d4a4770ac0726e83b3c3f6d","0xb781d1129ab645e892e30c216ed51bbc0152c503","0x30b9feec616a853e91aa1017fcc91b01d6325a49","0x871ed3e55aaeda4bd9d7fffc1f104c4f14257bcd","0x4ea80bddf61cbc59481a1036851887a925ec785e","0x9201dd56a367fe5d216d8aa40e1024f51fd7283a","0xdc213327be3dc6b8807140e9b3dda30e0941ccfd","0x7305ca9471a862f6abdcb7274f55d26c311db706","0xba4bbb372b7906544dd160774d8c5630bbbb257a","0xd5e4d79a1e53791f75013201830158d451995b64","0x5d8af9ba2d0905a151b31bef25d921d16c01bf8b","0x0a5ab64480a545d244e3cfe223ad8b05b0499b4f","0x59c51002a51f7b89b7fef94abf7180d7bc5a3603","0xaa18dc934f177432b02c871888784d4e783a461f","0x009525c93ea3b09330f98c076de98066f2d9a961","0x9741f5074ba71deebf2aea87d701af3d4f9ddefe","0x7c472cd10f2ae43d761add67a0dd70dd46791e73","0x86e9ef04689286f53555d56aa311b7e4404eb26c","0x2d53bcecd43cb384b371139cfead502ea5560c81","0xe2a7b458eba730fbc3c70d9dbeccea9e9cd7fd4e","0x288eafb94ed86219a96562d734e897a0b44a29cc","0xf2325e6b1661413106cf2cac9f6359e0aa414a24","0xa41def98d409856c1d8c8e3e1dcdf1501b77f088","0x8d6ac6e12c71db167cebca56228da94599db32bd","0x5f823ae5870e0998f220e6f5062717d8c6121360","0x679a6231bb86297aa28ea280ba3c43c37230fc18","0x52a38000018bbfc67956e09bac99f3cc4b2185b8","0xc28795ef77f83c561c50eaa2abde33c838130997","0x7d8ccf5db4a25197547a4a6213481fba22fbddbe","0xfdb2c50bd11ad11866712a8407c3417f889ae47c","0x8c62f8d8339a24e77d428d1dc8cd8c6e25c4d7b5"];

        //let test_data = vec!["******************************************"];
        //let test_data = vec!["******************************************"];
        //let test_pools : Vec<Address> = test_data.iter().map(|x| Address::from_str(x).unwrap()).collect();

        //vira.generate_mevs(Some(test_pools));
        let _ = vira.sm.generate_mevs(None);

        /*
        let mevs = &vira.sm.mevs;
        println!("pool_len: {}, mevs_len: {:?}", &vira.sm.data.len(), mevs.len());

        let mut first_mev = MevPath {..Default::default()};
        let mut count = 0;
        for (pool, mev) in mevs {
            count += 1;
            println!("------- ({}) pool: {:?} ----------", count,pool);
            vira.sm._display_mevs(&mev);
            mev.iter().enumerate().for_each(|(index, m)| {
                if index == 0 {
                    if count == 1 { first_mev = m.clone(); }
                    println!("{}: {:?}", index, m);
                    m.pools.iter().for_each(|p|{
                        let _p = vira.sm.data.get(&p.addr).unwrap();
                        println!("{}", _p);
                    });
                }
            });
        }

        let test_calc_lowest_val_vec : Vec<PoolIndex> = first_mev.pools.iter().map(|p| {
            let pool = &vira.sm.data.get(&p.addr).unwrap();
            PoolIndex {
                addr : p.addr.clone(),
                in_index : p.in_index,
                out_index : p.out_index,
                in_token : pool.data().tokens[p.in_index].addr,
                out_token : pool.data().tokens[p.out_index].addr,
            }
        }).collect();
        let (values, lowest) = vira.sm.calc_lowest_value(&test_calc_lowest_val_vec, None);
        println!("{:?}, {:?}", values.iter().map(|v| format_ether(*v)).collect::<Vec<String>>(), lowest);

        let weth = &vira.sm.tokens.get(&config.eth);
        println!("lowest: {:?}", weth.usd(lowest));
         */
    }

    
}


