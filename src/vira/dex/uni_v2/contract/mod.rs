use alloy::{
    dyn_abi::{DynSolType, DynSolValue}, primitives::{Address, U256},  sol,
};
use colored::Colorize;
use futures::future::ok;
use std::{str::FromStr, sync::Arc};

use crate::{connector::Connector, vira::{errors::DEXError, pool::{DexPool, PoolData, PoolDataToken, Status, SwapWay, POOL}}};

use super::pool::UniV2Pool;


sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetUniswapV2PoolAddressBatchRequest,
    "src/vira/dex/uni_v2/contract/GetUniswapV2PoolAddressBatchRequest.json"
}

sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetUniswapV2PoolDataBatchRequest,
    "src/vira/dex/uni_v2/contract/GetUniswapV2PoolDataBatchRequest.json"
}

sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetUniswapV2PoolReservesBatchRequest,
    "src/vira/dex/uni_v2/contract/GetUniswapV2PoolReservesBatchRequest.json"
}


#[derive(Debug, Clone)]
pub struct PoolDataUniV2OnChain {
    pub id: U256,
    pub addr: Address,
    pub token0: Address,
    pub token1: Address,
    pub s0: String,
    pub s1: String,
    pub r0: U256,
    pub r1: U256,
    pub d0: U256,
    pub d1: U256,
    pub last_update: U256,
    pub stable: bool,
    pub err: bool,
}

fn populate_pool_data_from_tokens(
    t: &[DynSolValue],
) -> Option<PoolDataUniV2OnChain> {
    Some(PoolDataUniV2OnChain {
        //id: t[0].as_uint()?.0,
        id : U256::ZERO,
        addr: t[0].as_address()?,
        token0: t[1].as_address()?,
        token1: t[2].as_address()?,
        s0: t[3].as_str()?.to_owned(),
        s1: t[4].as_str()?.to_owned(),

        r0: t[5].as_uint()?.0,
        r1: t[6].as_uint()?.0,
        
        d0: t[7].as_uint()?.0,
        d1: t[8].as_uint()?.0,
        last_update: t[9].as_uint()?.0,
        stable: t[10].as_bool()?,
        err: t[11].as_bool()?,

    })
}

/*
pub async fn get_dex_pools_batch_request(
    addr: Address,
    is_router: bool,
    from: usize,
    to: usize,
    connector: Arc<Connector>,
) -> Result<Vec<PoolDataUniV2OnChain>, DEXError>
{
    let deployer = IGetUniswapV2PoolsBatchRequest::deploy_builder(connector.provider(), addr, is_router, U256::from(from), U256::from(to));
    let res = deployer.call_raw().await?;
    //print!("res: {:#?}", res);
    let constructor_return = DynSolType::Array(Box::new(DynSolType::Tuple(vec![
        DynSolType::Uint(256),
        DynSolType::Address,
        DynSolType::Address,
        DynSolType::Address,
        DynSolType::String,
        DynSolType::String,
        DynSolType::Uint(112),
        DynSolType::Uint(112),
        DynSolType::Uint(8),
        DynSolType::Uint(8),
        DynSolType::Uint(32),
        DynSolType::Bool,
    ])));
    let return_data_tokens = constructor_return.abi_decode_sequence(&res).unwrap();

    let mut pools = vec![];

    if let Some(tokens_arr) = return_data_tokens.as_array() {
        for token in tokens_arr {
            //print!("token: {:#?}", token);
            if let Some(t) = token.as_tuple() {
                if let Some(data) = populate_pool_data_from_tokens(t) {
                    print!("{:}", data);
                    pools.push(data);
                } else {
                    println!("populate_pool_data_from_tokens failed !!");
                }
            }
        }
    }
    Ok(pools)
}
*/

pub async fn get_pools_address_batch_request(
    factory: Address,
    from: U256,
    step: U256,
    connector: Arc<Connector>,
) -> Result<Vec<Address>, DEXError>
{
    let deployer = IGetUniswapV2PoolAddressBatchRequest::deploy_builder(connector.provider(), from, step, factory);
    let res = deployer.call_raw().await?;

    let constructor_return = DynSolType::Array(Box::new(DynSolType::Address));
    let return_data_tokens = constructor_return.abi_decode_sequence(&res)?;

    let mut pairs = vec![];
    if let Some(tokens_arr) = return_data_tokens.as_array() {
        for token in tokens_arr {
            if let Some(addr) = token.as_address() {
                if !addr.is_zero() {
                    pairs.push(addr);
                }
            }
        }
    };

    Ok(pairs)
}

pub async fn get_pool_data_batch_request(
    pools: &mut [POOL],
    connector: Arc<Connector>,
) -> Result<(), DEXError>
{
    let mut target_addresses = vec![];
    for pool in pools.iter() {
        target_addresses.push(pool.data().addr);
    }

    let deployer = IGetUniswapV2PoolDataBatchRequest::deploy_builder(connector.provider(), target_addresses);
    let res = deployer.call().await?;

    let constructor_return = DynSolType::Array(Box::new(DynSolType::Tuple(vec![
        //DynSolType::Uint(256),
        DynSolType::Address,
        DynSolType::Address,
        DynSolType::Address,
        DynSolType::String,
        DynSolType::String,
        DynSolType::Uint(112),
        DynSolType::Uint(112),
        DynSolType::Uint(8),
        DynSolType::Uint(8),
        DynSolType::Uint(32),
        DynSolType::Bool,
        DynSolType::Bool,
    ])));
    let return_data_tokens = constructor_return.abi_decode_sequence(&res)?;

    let mut pool_idx = 0;
    if let Some(tokens_arr) = return_data_tokens.as_array() {
        for token in tokens_arr {
            if let Some(pool_data) = token.as_tuple() {
                // If the pool token A is not zero, signaling that the pool data was polulated
                //if let Some(address) = pool_data[1].as_address() {
                    //if !address.is_zero() {
                        // Update the pool data
                        if let POOL::UniV2Pool(uniswap_v2_pool) = pools.get_mut(pool_idx).expect("Pool idx should be in bounds")
                        {
                            if let Some(on_chain) = populate_pool_data_from_tokens(pool_data) {
                                if on_chain.err {
                                    println!("\n {}", format!("[ERR.univ2] pool_data: {}", on_chain.addr).red());
                                    //println!("pool data: {:#?}", pool);
                                }
                                update_pool_data(uniswap_v2_pool, on_chain);
                            }
                        }
                    //}
                //}
                pool_idx += 1;
            }
        }
    }

    Ok(())
}

fn update_pool_data(pool: &mut UniV2Pool, on_chain: PoolDataUniV2OnChain) {
    let data = pool.data_mut();
    data.ver = 2;
    data.swap_way = SwapWay::Pool;
    data.tokens = vec![
        PoolDataToken {
            addr: on_chain.token0,
            index: 0,
            symbol: on_chain.s0,
            decimal: on_chain.d0.to::<u8>(),
            reserve: on_chain.r0,
            fee: U256::ZERO,
            weight: U256::ZERO,
        },
        PoolDataToken {
            addr: on_chain.token1,
            index: 1,
            symbol: on_chain.s1,
            decimal: on_chain.d1.to::<u8>(),
            reserve: on_chain.r1,
            fee: U256::ZERO,
            weight: U256::ZERO,
        },
    ];
    data.stable = on_chain.stable;
    data.status = if on_chain.err { Status::Bad } else { Status::UnChecked };
    data.update_time = on_chain.last_update.to::<u64>();
}

//传入addrs，返回Vec<reserve0, reserve1, blockTimestampLast>
pub async fn get_pool_reserves_batch_request(
    addrs: Vec<Address>,
    connector: Arc<Connector>,
) -> Result<Vec<(U256, U256, u64)>, DEXError>
{
    let deployer = IGetUniswapV2PoolReservesBatchRequest::deploy_builder(connector.provider(), addrs);

    let res =  deployer.call_raw().await?;
    let return_data =<Vec<(U256, U256, u64)> as alloy::sol_types::SolValue>::abi_decode(&res)?;
    Ok(return_data)
}

#[cfg(test)]
mod tests {
    use std::ops::Add;

    use super::*;
    use alloy::providers::Provider;

    use crate::{config, connector::Connector, vira::dex::uni_v2::pool::UniV2Pool};


    #[tokio::test]
    async fn test_get_dex_pools_batch_request() {
        let config = config::pls::new();
        let connector = Arc::new(Connector::new(&config.server).await);
        let latest_block = connector.provider().get_block_number().await.unwrap();
        println!("Latest block number: {latest_block}");
        let pairs = get_pools_address_batch_request( Address::from_str("0x1715a3e4a142d8b698131108995174f37aeba10d").unwrap(), U256::from(0), U256::from(3), connector.clone()).await.unwrap();
        println!("pairs: {:#?}", pairs);
    }

}