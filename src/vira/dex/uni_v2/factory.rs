use std::sync::Arc;
use alloy::{eips::BlockId, primitives::{Address, B256, U256}, sol, sol_types::{SolEvent, SolValue}};
use colored::Colorize;
use futures::{StreamExt, stream};
use itertools::Itertools;
use serde::{Deserialize, Serialize};
use std::sync::atomic::{AtomicUsize, Ordering};

use crate::{connector::Connector, tools, vira::{dex::factory::{DexFactory, FactoryConfig, FACTORY}, errors::DEXError, pool::{DexPool, PoolData, PoolDataToken, Status, POOL}, util}, CONFIG};

use super::{contract, pool::{self, UniV2Pool}};


sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetUniswapV2PoolAddressBatchRequest,
    "forge/out/GetUniswapV2PoolAddressBatchRequest.sol/GetUniswapV2PoolAddressBatchRequest.json"
}

sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    IGetUniswapV2PoolDataBatchRequest,
    "forge/out/GetUniswapV2PoolDataBatchRequest.sol/GetUniswapV2PoolDataBatchRequest.json"
}

sol! {
    /// Interface of the UniswapV2Factory contract
    #[derive(Debug, PartialEq, Eq)]
    #[sol(rpc)]
    contract IUniswapV2Factory {
        event PairCreated(address indexed token0, address indexed token1, address pair, uint256 index);
        function getPair(address tokenA, address tokenB) external view returns (address pair);
        function allPairs(uint256 index) external view returns (address pair);
        function allPairsLength() external view returns (uint256 length);
    }
}

type PoolDataRes = (Address, Address, String, String, U256, U256, u16, u16, u64, bool, bool);

#[derive(Default, Debug, Clone, Serialize, Deserialize)]
pub struct UniV2Factory {
    pub data : FactoryConfig,

    pub indexed : usize, //对比链上的allPairsLength，增量更新
}

impl UniV2Factory {
    //获取v2的增量pair address, 存储当前进度
    pub async fn import_new_pool(&mut self, connector: Arc<Connector>) -> Result<Vec<Address>, DEXError>
    {
        let connector_arc = connector.clone();
        let factory = IUniswapV2Factory::new(self.address(), connector_arc.provider());

        let length = factory.allPairsLength().call().await?;
        println!("\n updating v2 pools {} length: {:?}", self.address(), length);
        let pairs_length = length.to::<usize>();
        let mut addrs = vec![];
        if self.indexed == pairs_length {
            //不需要更新
            return Ok(addrs);
        }

        // NOTE: max batch size for this call until codesize is too large
        let step = 665;
        let mut idx_from = self.indexed;
        let mut idx_to = if step + idx_from > pairs_length {
            pairs_length
        } else {
            step + idx_from
        };

        for _ in (idx_from..pairs_length).step_by(step) {
            eprint!("\r updating address: {}/{}", idx_from, pairs_length);
            
            addrs.append(
                &mut contract::get_pools_address_batch_request(
                    self.address(),
                    U256::from(idx_from),
                    U256::from(idx_to),
                    connector.clone(),
                )
                .await?,
            );

            idx_from = idx_to;

            if idx_to + step > pairs_length {
                idx_to = pairs_length
            } else {
                idx_to += step;
            }
        }

        // Create new empty pools for each pair
        for addr in &addrs {
            let mut pool = UniV2Pool {..Default::default()};
            let data = pool.data_mut();
            data.addr = addr.clone();
            data.fp = self.data.fee; //pair fee
            //data.tokens = vec![PoolDataToken{..Default::default()}, PoolDataToken{..Default::default()}];
            self.data.pools.push(POOL::UniV2Pool(pool));
        }
        //存储当前更新的进度
        self.indexed = pairs_length;
        Ok(addrs)
    }

    async fn discover_new_pools(&mut self, connector: Arc<Connector>) -> Result<Vec<Address>, DEXError> {
        let factory = IUniswapV2Factory::new(self.address(), connector.provider.clone());
        let pairs_length = factory
            .allPairsLength()
            .call()
            .await?
            .to::<usize>();

        println!("\n获取所有V2池子 {} 总数: {:?}", self.address(), pairs_length);

        if self.indexed >= pairs_length {
            return Ok(Vec::new());
        }
        self.indexed = pairs_length;

        let step = 766;
        let processed = Arc::new(AtomicUsize::new(0));
        let factory_address = self.address(); // 克隆地址以避免借用冲突
        
        // 使用 iter() 和 buffer_unordered() 替代 FuturesUnordered
        // 这样可以更好地控制并发数量，避免一次性创建过多的 future
        let futures = stream::iter((0..pairs_length).step_by(step).map(move |i| {
            let deployer = IGetUniswapV2PoolAddressBatchRequest::deploy_builder(
                connector.provider().clone(),
                U256::from(i),
                U256::from(step),
                factory_address,
            );
            
            let processed_clone = processed.clone();
            let pairs_length = pairs_length; // 克隆变量以避免闭包中的借用冲突
            async move {
                let res = deployer.call_raw().await?;
                let return_data = <Vec<Address> as SolValue>::abi_decode(&res)?;
                
                let current = processed_clone.fetch_add(step, Ordering::SeqCst);
                eprint!("\r获取池子地址进度: {}/{} ({:.1}%)", 
                       current + step.min(pairs_length - current), 
                       pairs_length, 
                       (current as f64 + step.min(pairs_length - current) as f64) / pairs_length as f64 * 100.0);
                
                Ok::<Vec<Address>, DEXError>(return_data)
            }
        }))
        .buffer_unordered(CONFIG.concurrency.max_task); // 使用配置的并发数控制同时执行的 future 数量

        let mut pairs = Vec::new();
        futures::pin_mut!(futures);
        while let Some(res) = futures.next().await {
            let tokens = res?;
            for token in tokens {
                if !token.is_zero() {
                    pairs.push(token);
                }
            }
        }
        println!("\n成功获取池子地址: {}", pairs.len());
        Ok(pairs)
    }

    pub async fn sync_all_pools(
        pools: Vec<POOL>,
        _block_number: BlockId,
        connector: Arc<Connector>,
    ) -> Result<Vec<POOL>, DEXError> {
        let total_pools = pools.len();
        //println!("\n同步所有V2池子数据，总数: {}", total_pools);
        
        let pairs = pools
            .iter()
            .chunks(CONFIG.concurrency.batch_sync)
            .into_iter()
            .map(|chunk| chunk.map(|amm| amm.addr()).collect())
            .collect::<Vec<Vec<Address>>>();

        let total_groups = pairs.len();
        let processed = Arc::new(AtomicUsize::new(0));
        
        // 使用 iter() 和 buffer_unordered() 替代 FuturesUnordered
        // 这样可以更好地控制并发数量，避免一次性创建过多的 future
        let futures = stream::iter(pairs.into_iter().map(|group| {
            let provider = connector.provider().clone();
            let deployer = IGetUniswapV2PoolDataBatchRequest::deploy_builder(
                provider.clone(),
                group.clone(),
            ).gas(20_000_000u64);
            
            let processed_clone = processed.clone();
            let group_size = group.len();

            async move {
                let res = deployer.call_raw().await;
                
                let current_batch = processed_clone.fetch_add(1, Ordering::SeqCst) + 1;
                let processed_pools = (current_batch - 1) * CONFIG.concurrency.batch_sync + group_size;
                eprint!("\r同步池子数据进度: 批次 {}/{} ({:.1}%), 池子 ~{}/{} ({:.1}%)", 
                       current_batch, 
                       total_groups, 
                       current_batch as f64 / total_groups as f64 * 100.0,
                       processed_pools.min(total_pools),
                       total_pools,
                       processed_pools.min(total_pools) as f64 / total_pools as f64 * 100.0);
                
                match res {
                    Ok(res) => {
                        let return_data = <Vec<PoolDataRes> as SolValue>::abi_decode(&res)?;
                        util::delay(CONFIG.concurrency.delay_ms_on_low_level_request).await;
                        Ok((group, return_data))
                    },
                    Err(e) => {
                        println!("\n{}", format!("[ERR] IGetUniswapV2PoolDataBatchRequest, len:{} err: {}", group.len(), e).red());
                        //改成单个更新
                        let mut group_tmp = vec![];
                        let mut return_data_tmp = vec![];
                        let single_processed = Arc::new(AtomicUsize::new(0));
                        
                        for pool_addr in group.clone() {
                            let deployer_single = IGetUniswapV2PoolDataBatchRequest::deploy_builder(
                                provider.clone(),
                                vec![pool_addr],
                            );
                            let res_single = deployer_single.call_raw().await;
                            
                            let current = single_processed.fetch_add(1, Ordering::SeqCst) + 1;
                            eprint!("\r单个同步进度: {}/{} ({:.1}%)", 
                                   current, 
                                   group.len(), 
                                   current as f64 / group.len() as f64 * 100.0);
                            
                            match res_single {
                                Ok(res) => {
                                    let res_single_data = <Vec<PoolDataRes> as SolValue>::abi_decode(&res)?;
                                    group_tmp.push(pool_addr);
                                    return_data_tmp.push(res_single_data[0].clone());
                                },
                                Err(e) => {
                                    println!("\n{}", format!("[ERR] IGetUniswapV2PoolDataBatchRequest single {} err: {}", pool_addr, e).red());
                                    continue;
                                }
                            }
                            util::delay(CONFIG.concurrency.delay_ms_on_low_level_request).await;
                        }
                        println!(""); // 换行
                        
                        Ok::<(Vec<Address>, Vec<PoolDataRes>), DEXError>((
                            group_tmp,
                            return_data_tmp,
                        ))
                    }
                    
                }
            }
        }))
        .buffer_unordered(CONFIG.concurrency.max_task); // 使用配置的并发数控制同时执行的 future 数量

        let mut pools_map = pools
            .into_iter()
            .map(|pool| (pool.addr(), pool))
            .collect::<std::collections::HashMap<_, _>>();

        futures::pin_mut!(futures);
        while let Some(res) = futures.next().await {
            let (group, return_data) = res?;
            if group.len() != return_data.len() {
                panic!("group length != return_data length");
            }

            for (pool_data, pool_address) in return_data.iter().zip(group.iter()) {
                // If the pool token A is not zero, signaling that the pool data was polulated
                if pool_data.0.is_zero() {
                    continue;
                }

                let pool = pools_map.get_mut(pool_address).unwrap();

                let POOL::UniV2Pool(pool) = pool else {
                    // TODO:: We should never receive a non UniswapV2Pool AMM here, we can handle this more gracefully in the future
                    panic!("Unexpected pool type")
                };
                let data = pool.data_mut();
                data.tokens = vec![
                    PoolDataToken {
                        addr: pool_data.0,
                        index: 0,
                        symbol: pool_data.2.clone(),
                        reserve: pool_data.4,
                        decimal: pool_data.6 as u8,
                        ..Default::default()
                    },
                    PoolDataToken {
                        addr: pool_data.1,
                        index: 1,
                        symbol: pool_data.3.clone(),
                        reserve: pool_data.5,
                        decimal: pool_data.7 as u8,
                        ..Default::default()
                    }
                ];
                data.update_time = pool_data.8;
                data.stable = pool_data.9;
                data.status = if pool_data.10 {
                    Status::Bad
                } else {
                    Status::UnChecked
                };
                //println!("{}", pool);
            }
        }

        println!("\n过滤无效池子...");
        
        // 过滤有效池子
        let valid_pools = pools_map
            .into_iter()
            .filter_map(|(_, pool)| {
                let data = pool.data();
                if data.status == Status::Bad || data.tokens.iter().any(|t| t.reserve.is_zero()) {
                    None
                } else {
                    Some(pool)
                }
            })
            .collect::<Vec<POOL>>();
        
        println!("同步完成，有效池子: {}/{}", valid_pools.len(), total_pools);
        
        Ok(valid_pools)
    }

}

impl DexFactory for UniV2Factory {
    fn new(config: FactoryConfig) -> FACTORY {
        FACTORY::UniV2Factory(UniV2Factory {
            data: config,
            ..Default::default()
        })
    }

    fn data(&self) -> &FactoryConfig {
        &self.data
    }

    fn data_mut(&mut self) -> &mut FactoryConfig {
        &mut self.data
    }

    fn pool_creation_event(&self) -> B256 {
        IUniswapV2Factory::PairCreated::SIGNATURE_HASH
    }

    //增量更新pools, 返回新增的address
    async fn update_pools(&mut self, connector: Arc<Connector>) -> Result<Vec<Address>, DEXError>{
        Ok(self.import_new_pool(connector).await?)
    }

    async fn sync(&self, pools: Vec<POOL>, _to_block: BlockId, connector: Arc<Connector>) -> Result<Vec<POOL>, DEXError> {
        Self::sync_all_pools(pools, _to_block, connector).await
    }

    async fn discover(&mut self, _to_block: BlockId, connector: Arc<Connector>) -> Result<Vec<POOL>, DEXError> {
        let pairs = self.discover_new_pools(connector).await?;
        
        Ok(pairs
            .into_iter()
            .map(|addr|
                POOL::UniV2Pool(
                    UniV2Pool {
                        data: PoolData {
                            addr,
                            fp : self.data.fee,
                            ver : 2,
                            swap_way : crate::vira::pool::SwapWay::Pool,
                            ..Default::default()
                        }
                    }
                ))
            .collect()
        )
    }

    /*
    async fn update_reservers(pools: &mut [POOL], connector: Arc<Connector>) -> Result<(), DEXError> {
        let step = 250;
        let mut loop_count = 1;
        let total = pools.len();
        for pool_chunk in pools.chunks_mut(step) {
            eprint!("\r update_reservers: {:?}/{:?} ({:.1}%)", step * loop_count, total, (step as f64 * loop_count as f64 / total as f64) * 100.0);
            match contract::get_pool_reserves_batch_request(pool_chunk, connector.clone()).await {
                Ok(_) => (),
                Err(e) => {
                    println!("{}", e);
                    for pool_single in pool_chunk.chunks_mut(1) {
                        match contract::get_pool_reserves_batch_request(pool_single, connector.clone()).await {
                            Ok(_) => (),
                            Err(e) => {
                                eprint!("\n {}", format!("[ERR] update_single_reserver: {}", pool_single[0].data().addr).red());
                                pool_single[0].data_mut().status = Status::Unknown;
                            }
                        }
                    }
                }
            };
            loop_count += 1;
        }
        Ok(())
    }
     */
    }


#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config, connector::Connector, vira::dex::factory::DexFactory};
    use std::sync::Arc;

    #[tokio::test]
    async fn test_update_pools() {
        //println!("test_update_pools");
        //UniV2Factory::new(FactoryConfig::new().name("plsx").addr("0x1715a3e4a142d8b698131108995174f37aeba10d").fee(9971));
        let config = config::pls::new();
        let connector = Arc::new(Connector::new(&config.server).await);
        let mut factory = UniV2Factory::new(FactoryConfig::new().name("u6").addr("0x3a0fa7884dd93f3cd234bbe2a0958ef04b05e13b").fee(9975));
        factory.update_pools(connector.clone()).await.expect("Failed to update pools");
        
        match factory.update_pools(connector).await {
            Ok(_) => println!("update_pools test passed"),
            Err(e) => panic!("update_pools test failed: {:?}", e),
        }
    }

    #[tokio::test]
    async fn test_populate_pool_data() {
        let config = config::pls::new();
        let connector = Arc::new(Connector::new(&config.server).await);
        let mut factory = UniV2Factory::new(FactoryConfig::new().name("u6").addr("0x3a0fa7884dd93f3cd234bbe2a0958ef04b05e13b").fee(9975));
        
        // 更新pools地址
        factory.update_pools(connector.clone()).await.expect("Failed to update pools");
        // 更新pools数据
        //UniV2Factory::sync(factory.pools_mut(), connector.clone()).await.expect("Failed to populate data");
        let pools = factory.pools();
        dbg!(pools.len());
        // 打印前五个pool
        println!("前五个pool:");
        for (_, pool) in pools.iter().take(5).enumerate() {
            println!("{}", pool);
        }
    }
}
